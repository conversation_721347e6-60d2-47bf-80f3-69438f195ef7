const { 
  createRazorpayOrder, 
  verifyPaymentSignature, 
  verifyWebhookSignature,
  getPaymentDetails 
} = require('../config/razorpay');
const { Cart } = require('../models/associations');
const { User } = require('../models/associations');

// Create Razorpay order
const createOrder = async (req, res) => {
  try {
    const userId = req.userId;

    // Get user's active cart
    const cart = await Cart.findByUserId(userId);
    if (!cart || cart.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Cart is empty'
      });
    }

    // Get user information
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate final amount
    const paymentDetails = cart.getPaymentDetails();
    const finalAmount = paymentDetails.amount_payable;

    // Create unique receipt ID
    const receiptId = `receipt_${userId}_${Date.now()}`;

    // Prepare order data for Razorpay
    const orderData = {
      amount: finalAmount, // Amount in rupees
      currency: 'INR',
      receipt: receiptId,
      notes: {
        user_id: userId,
        cart_id: cart.id,
        user_email: user.email,
        user_name: user.name || user.first_name + ' ' + user.last_name
      }
    };

    // Create Razorpay order
    const razorpayOrder = await createRazorpayOrder(orderData);

    res.status(200).json({
      success: true,
      message: 'Razorpay order created successfully',
      data: {
        order_id: razorpayOrder.id,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        receipt: razorpayOrder.receipt,
        key_id: process.env.RAZORPAY_KEY_ID,
        user: {
          name: user.name || user.first_name + ' ' + user.last_name,
          email: user.email,
          contact: user.phone || ''
        },
        cart_details: {
          items: cart.invoice_items,
          total_amount: cart.total_amount,
          discount_amount: cart.discount_amount,
          delivery_fee: cart.delivery_fee,
          final_amount: finalAmount
        }
      }
    });

  } catch (error) {
    console.error('Create Razorpay order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment order',
      error: error.message
    });
  }
};

// Verify payment and complete order
const verifyPayment = async (req, res) => {
  try {
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature,
      address_id 
    } = req.body;

    // Validate required fields
    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature || !address_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required payment verification data'
      });
    }

    // Verify payment signature
    const isValidSignature = verifyPaymentSignature(
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature
    );

    if (!isValidSignature) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment signature'
      });
    }

    // Get payment details from Razorpay
    const paymentDetails = await getPaymentDetails(razorpay_payment_id);

    // Store payment information in request for checkout controller
    req.body.payment_method = 'RAZORPAY';
    req.body.payment_details = {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      payment_status: paymentDetails.status,
      payment_method: paymentDetails.method,
      amount_paid: paymentDetails.amount / 100, // Convert from paise to rupees
      currency: paymentDetails.currency,
      captured: paymentDetails.captured,
      created_at: new Date(paymentDetails.created_at * 1000)
    };

    // Import and call the complete order function
    const { completeOrder } = require('./checkoutController');
    await completeOrder(req, res);

  } catch (error) {
    console.error('Verify payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Payment verification failed',
      error: error.message
    });
  }
};

// Handle Razorpay webhooks
const handleWebhook = async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const body = req.body;

    // Verify webhook signature
    const isValidWebhook = verifyWebhookSignature(body, signature);

    if (!isValidWebhook) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook signature'
      });
    }

    // Handle different webhook events
    const event = body.event;
    const payload = body.payload;

    switch (event) {
      case 'payment.captured':
        console.log('Payment captured:', payload.payment.entity);
        // Handle successful payment
        break;
      
      case 'payment.failed':
        console.log('Payment failed:', payload.payment.entity);
        // Handle failed payment
        break;
      
      case 'order.paid':
        console.log('Order paid:', payload.order.entity);
        // Handle order completion
        break;
      
      default:
        console.log('Unhandled webhook event:', event);
    }

    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('Webhook handling error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
};

module.exports = {
  createOrder,
  verifyPayment,
  handleWebhook
};
