const { 
  validateRazorpayConfig, 
  createRazorpayOrder, 
  verifyPaymentSignature 
} = require('../config/razorpay');

async function testRazorpayIntegration() {
  console.log('🧪 Testing Razorpay Integration...\n');

  // Test 1: Configuration Validation
  console.log('1. Testing Razorpay Configuration...');
  const isConfigValid = validateRazorpayConfig();
  
  if (isConfigValid) {
    console.log('✅ Razorpay configuration is valid');
  } else {
    console.log('❌ Razorpay configuration is invalid');
    console.log('Please check your RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in .env file');
    return;
  }

  // Test 2: Order Creation
  console.log('\n2. Testing Order Creation...');
  try {
    const testOrderData = {
      amount: 100, // ₹1.00 for testing
      currency: 'INR',
      receipt: `test_receipt_${Date.now()}`,
      notes: {
        test: 'true',
        environment: 'development'
      }
    };

    const order = await createRazorpayOrder(testOrderData);
    console.log('✅ Order created successfully');
    console.log('Order ID:', order.id);
    console.log('Amount:', order.amount / 100, 'INR');
    console.log('Status:', order.status);

    // Test 3: Signature Verification (Mock)
    console.log('\n3. Testing Signature Verification...');
    
    // Mock payment data for testing signature verification logic
    const mockOrderId = order.id;
    const mockPaymentId = 'pay_test123456789';
    const mockSignature = 'mock_signature_for_testing';
    
    // This will fail as expected since it's mock data
    const isValidSignature = verifyPaymentSignature(mockOrderId, mockPaymentId, mockSignature);
    
    if (isValidSignature) {
      console.log('✅ Signature verification passed (unexpected for mock data)');
    } else {
      console.log('✅ Signature verification correctly failed for mock data');
    }

    console.log('\n🎉 Razorpay integration test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Update .env with your actual Razorpay credentials');
    console.log('2. Test the payment flow in your application');
    console.log('3. Use Razorpay test cards for testing payments');

  } catch (error) {
    console.log('❌ Order creation failed:', error.message);
    
    if (error.message.includes('key_id')) {
      console.log('💡 Tip: Make sure your RAZORPAY_KEY_ID is correct');
    }
    
    if (error.message.includes('key_secret')) {
      console.log('💡 Tip: Make sure your RAZORPAY_KEY_SECRET is correct');
    }
  }
}

// Test environment variables
function testEnvironmentVariables() {
  console.log('🔧 Environment Variables Check:\n');
  
  const requiredVars = [
    'RAZORPAY_KEY_ID',
    'RAZORPAY_KEY_SECRET'
  ];

  const optionalVars = [
    'RAZORPAY_WEBHOOK_SECRET'
  ];

  console.log('Required Variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 8)}...`);
    } else {
      console.log(`❌ ${varName}: Not set`);
    }
  });

  console.log('\nOptional Variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: Set`);
    } else {
      console.log(`⚠️  ${varName}: Not set (optional)`);
    }
  });

  console.log('\n');
}

// Run tests
async function runTests() {
  require('dotenv').config();
  
  console.log('🚀 Razorpay Integration Test Suite\n');
  console.log('='.repeat(50));
  
  testEnvironmentVariables();
  await testRazorpayIntegration();
  
  console.log('\n' + '='.repeat(50));
  console.log('Test completed. Check the results above.');
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testRazorpayIntegration,
  testEnvironmentVariables,
  runTests
};
