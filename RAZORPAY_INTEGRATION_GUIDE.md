# Razorpay Payment Integration Guide

## Overview
This guide explains how to set up and use the Razorpay payment integration in your e-commerce application.

## Prerequisites
1. Razorpay account (Sign up at https://razorpay.com/)
2. Razorpay API keys (Key ID and Key Secret)
3. Node.js application with Express.js

## Setup Instructions

### 1. Get Razorpay Credentials
1. Log in to your Razorpay Dashboard
2. Go to Settings → API Keys
3. Generate API Keys if not already done
4. Copy the Key ID and Key Secret

### 2. Configure Environment Variables
Update your `backend/.env` file with your Razorpay credentials:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=your_actual_razorpay_key_id
RAZORPAY_KEY_SECRET=your_actual_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_optional
```

**Important**: Replace the placeholder values with your actual Razorpay credentials.

### 3. Test Mode vs Live Mode
- **Test Mode**: Use test API keys for development and testing
- **Live Mode**: Use live API keys for production

Test card details for testing:
- Card Number: 4111 1111 1111 1111
- CVV: Any 3 digits
- Expiry: Any future date

## Features Implemented

### Backend Features
1. **Razorpay Configuration** (`backend/config/razorpay.js`)
   - Razorpay instance initialization
   - Order creation
   - Payment verification
   - Webhook handling

2. **Payment Controller** (`backend/controllers/razorpayController.js`)
   - Create Razorpay orders
   - Verify payment signatures
   - Handle webhooks

3. **Updated Checkout Controller**
   - Support for both COD and Razorpay payments
   - Dynamic payment status handling

### Frontend Features
1. **Payment Method Selection**
   - Radio buttons for COD vs Online Payment
   - Dynamic button text updates

2. **Razorpay Integration**
   - Razorpay checkout script integration
   - Payment flow handling
   - Error handling and user feedback

## API Endpoints

### Create Razorpay Order
```
POST /api/razorpay/create-order
Authorization: Bearer <token>
```

### Verify Payment
```
POST /api/razorpay/verify-payment
Authorization: Bearer <token>
Content-Type: application/json

{
  "razorpay_order_id": "order_xxx",
  "razorpay_payment_id": "pay_xxx",
  "razorpay_signature": "signature_xxx",
  "address_id": 123
}
```

### Webhook Handler
```
POST /api/razorpay/webhook
Content-Type: application/json
X-Razorpay-Signature: <signature>
```

## Payment Flow

### 1. User Selects Payment Method
- User chooses between "Cash on Delivery" or "Pay Online"
- Button text updates dynamically

### 2. COD Flow
- Direct order creation
- Payment status: "pending"
- Order status: "confirmed"

### 3. Razorpay Flow
1. Create Razorpay order via API
2. Open Razorpay checkout modal
3. User completes payment
4. Verify payment signature
5. Create order with payment details
6. Redirect to success page

## Testing the Integration

### 1. Start the Backend Server
```bash
cd backend
npm start
```

### 2. Test Payment Methods
1. Add products to cart
2. Go to checkout page
3. Select shipping address
4. Choose payment method:
   - **COD**: Should create order immediately
   - **Razorpay**: Should open payment modal

### 3. Test Razorpay Payments
1. Select "Pay Online" option
2. Click "Pay Now" button
3. Use test card details in Razorpay modal
4. Complete payment
5. Verify order creation and redirect

## Security Considerations

1. **API Keys**: Never expose secret keys in frontend code
2. **Signature Verification**: Always verify payment signatures
3. **Webhook Security**: Validate webhook signatures
4. **HTTPS**: Use HTTPS in production
5. **Input Validation**: Validate all payment-related inputs

## Troubleshooting

### Common Issues

1. **"Razorpay not configured" Error**
   - Check if RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET are set in .env
   - Restart the server after updating .env

2. **Payment Modal Not Opening**
   - Check browser console for JavaScript errors
   - Ensure Razorpay script is loaded
   - Verify API response structure

3. **Payment Verification Failed**
   - Check signature verification logic
   - Ensure correct key_secret is used
   - Verify payment response format

4. **Webhook Issues**
   - Configure webhook URL in Razorpay dashboard
   - Verify webhook signature validation
   - Check webhook event handling

## Production Deployment

1. **Environment Variables**
   - Use live API keys
   - Set secure webhook secrets
   - Configure proper CORS settings

2. **Webhook Configuration**
   - Set webhook URL in Razorpay dashboard
   - Handle all relevant webhook events
   - Implement proper logging

3. **Error Handling**
   - Implement comprehensive error logging
   - Set up monitoring and alerts
   - Handle payment failures gracefully

## Support

For Razorpay-specific issues:
- Documentation: https://razorpay.com/docs/
- Support: https://razorpay.com/support/

For integration issues:
- Check server logs
- Verify API responses
- Test with Razorpay's test environment
