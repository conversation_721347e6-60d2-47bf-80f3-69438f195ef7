const express = require('express');
const router = express.Router();
const {
  createOrder,
  verifyPayment,
  handleWebhook
} = require('../controllers/razorpayController');
const { authenticateToken } = require('../middleware/auth');

// POST /api/razorpay/create-order - Create Razorpay order (requires authentication)
router.post('/create-order', authenticateToken, createOrder);

// POST /api/razorpay/verify-payment - Verify payment and complete order (requires authentication)
router.post('/verify-payment', authenticateToken, verifyPayment);

// POST /api/razorpay/webhook - Handle Razorpay webhooks (no authentication required)
router.post('/webhook', handleWebhook);

module.exports = router;
